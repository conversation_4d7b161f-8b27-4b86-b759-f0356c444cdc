package consts

import "context"

// OnMessage 消息處理器函數類型
// 用於處理從 RabbitMQ 接收到的消息
type OnMessage func(ctx context.Context, message any)

const (
	CatalogGemini      = "gemini"
	CatalogAoAi        = "aoai"
	CatalogRouter      = "aiRouter"
	CatalogReqRes      = "http-logs"
	CatalogDSH         = "dsh"
	CatalogAMS         = "ams"
	CatalogMQ          = "mq"
	CatalogStorage     = "storage"
	CatalogOmniChannel = "omniChannel"
	CatalogCrawl       = "crawler"
	CatalogEmbedding   = "embedding"
	ContentMediaFile   = "media"
	ContentTypeText    = "text"
	AnswerSep          = `\n`
	QRSep              = `^^`
	KeyURLContent      = "URLContent:%s:%s" // tenant id ,service id
	KeyUploadFiles     = "Files:%s:%s"      //tenant id ,service id
	KeyWebsite         = "Website:%s:%s"    // tenant id  service id

	LineSystemInstruction = "prompts.line_system_instruction"
	ChatSystemInstruction = "prompts.chat_system_instruction"
)
const (
	RoleUser = "user"
	RoleAi   = "ai"
)

var VideoFormat = []string{
	"video/mp4",
	"video/3gpp",
	"video/mpeg",
	"video/mov",
	"video/avi",
	"video/x-flv",
	"video/mpg",
	"video/webm",
	"video/wmv",
}
var PhotoFormat = []string{
	"image/jpeg",
	"image/png",
	"image/heic",
	"image/heif",
	"image/webp",
}
var AudioFormat = []string{
	"audio/wav",
	"audio/mp3",
	"audio/ogg",
	"audio/aac",
	"audio/aiff",
	"audio/flac",
}
var DocumentFormat = []string{
	"application/pdf",
	"application/x-javascript",
	"text/javascript",
	"application/x-python",
	"text/python",
	"text/plain",
	"text/html",
	"text/css",
	"text/md",
	"text/csv",
	"text/xml",
	"text/rtf",
}
var FileSuffix = []string{
	"pptx", "docx", "xlsx", "xls", "pdf", "md",
}

const MinFileSize = 1024 * 1024 * 20
const MaxFileSize = 1024 * 1024 * 50
const MaxFileToken = 500000
const GeminiMaxTokens = 1040000

var ServiceName = "brainHub.svc"

const (
	AOAI           = "aoai"
	VertexAIGemini = "gemini"
	VertexAIClaude = "vertex.claude"
)

// Embedding 提供商常量
const (
	EmbeddingAOAI        = "aoai"
	EmbeddingOpenAI      = "openai"
	EmbeddingVertexAI    = "vertex"
	EmbeddingHuggingFace = "huggingface"
	EmbeddingLocal       = "local"
)

// Embedding 相關常數
const (
	EmbeddingMaxRetryAttempts = 3                        // 最大重試次數
	EmbeddingRetryDelaySecond = 2                        // 重試延遲秒數
	EmbeddingDefaultBatchSize = 100                      // 默認批次大小
	EmbeddingDefaultTimeout   = 30                       // 默認超時時間（秒）
	EmbeddingDefaultModel     = "text-embedding-ada-002" // 默認模型名稱
	EmbeddingDefaultDimension = 1536                     // 默認向量維度
	EmbeddingMaxTextLength    = 8192                     // 最大文本長度
	EmbeddingCacheKeyPrefix   = "embedding:"             // 緩存鍵前綴
	EmbeddingCacheTTL         = 3600                     // 緩存過期時間（秒）
)

// AoAi 相關常數
const (
	AoAiMaxRetryAttempts = 3               // 最大重試次數
	AoAiRetryDelaySecond = 2               // 重試延遲秒數
	AoAiTokenThreshold   = 15000           // Token 數量閾值 (提升至適合現代模型的水平)
	AoAiSummaryTemp      = 0.3             // 總結時使用的溫度
	AoAiDefaultModel     = "gpt-3.5-turbo" // 預設模型名稱
	AoAiDefaultEncoding  = "cl100k_base"   // 預設編碼
	AoAiTokenOverhead    = 3               // 每個請求的基本 token 開銷
	AoAiMsgOverhead      = 4               // 每條消息的格式 token 開銷

	// 新增的 Token 管理常數
	AoAiTokenCheckThreshold     = 0.85 // Token 檢查閾值百分比 (85%)
	AoAiAttachmentCheckInterval = 10   // 附件處理時的檢查間隔
	AoAiTokenCacheTimeout       = 30   // Token 計算結果緩存超時時間(秒)
)

// Claude 相關常數
const (
	CatalogClaude          = "claude"                     // Claude 日誌分類
	ClaudeMaxRetryAttempts = 3                            // 最大重試次數
	ClaudeRetryDelaySecond = 2                            // 重試延遲秒數
	ClaudeTokenThreshold   = 180000                       // Token 數量閾值 (Claude 支持更大的上下文)
	ClaudeSummaryTemp      = 0.3                          // 總結時使用的溫度
	ClaudeDefaultModel     = "claude-3-5-sonnet-20241022" // 預設模型名稱
	ClaudeMaxTokens        = 200000                       // Claude 最大 token 數量
	ClaudeTokenOverhead    = 10                           // 每個請求的基本 token 開銷
	ClaudeMsgOverhead      = 5                            // 每條消息的格式 token 開銷
)

// channel type
const (
	// ChLine from line
	ChLine = "line"
	// ChFB from facebook
	ChFB = "facebook"
	// ChIns from instagram
	ChIns = "instagram"
	// ChGoogle from google message
	ChGoogle = "google_message"
	// ChWeb from web
	ChWeb = "web"
)
const XHeaderService = "X-SERVICE"
const (
	MesssageTypeText = "text"
	MessageTypeImage = "image"
	MessageTypeVoice = "voice"
)
