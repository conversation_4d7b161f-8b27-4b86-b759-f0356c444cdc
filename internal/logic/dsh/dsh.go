package dsh

import (
	"brainHub/boot"
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"brainHub/internal/service"
	"context"
	"fmt"
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/net/gsvc"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"time"
)

type sDSH struct {
	client *gclient.Client
}

func init() {
	service.RegisterDSH(New())
}

func New() service.IDSH {
	boot.WaitReady()
	s := &sDSH{
		client: g.Client(),
	}
	s.client.SetDiscovery(gsvc.GetRegistry())

	return s
}

func (s *sDSH) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogDSH)
}

func (s *sDSH) sendMessage(ctx context.Context, action string, data []byte) (err error) {

	return service.MessageQ().Send(
		ctx,
		consts.RouteKeyBrainHub,
		action,
		data,
	)

}

func (s *sDSH) GetResources(ctx context.Context, tenantID, serviceID string) (records []*model.ResourceRecord, err error) {
	s.logger().Debugf(ctx, "get resources for tenant [%v]   service [%v]  ", tenantID, serviceID)

	tableName := fmt.Sprintf(consts.TableResourcesPattern, serviceID)
	whereCond := "service_id=? "
	params := g.Slice{serviceID}

	req := &model.GetContentsReq{
		Schema:    tenantID,
		Table:     tableName,
		WhereCond: whereCond,
		Params:    params,
	}
	var res *model.GetContentsRes
	if err = s.sendRequest(ctx, consts.UriDSHGetContents, req, &res); err != nil {
		return
	}
	if res != nil {
		if res.Code != consts.Success.Code() {
			err = gerror.New(res.Message)
			s.logger().Error(ctx, err)
			return
		}
		records = make([]*model.ResourceRecord, 0)
		if len(res.Contents) > 0 {
			_ = gconv.Structs(res.Contents, &records)
		}
	}

	return
}

func (s *sDSH) getChatMessageFormCache(ctx context.Context, tenantID, serviceID, userID, channel string) (messages []string) {
	s.logger().Debugf(ctx, "get chat messages from cache for tenant [%v] and service [%v] and user [%v] and channel [%v]",
		tenantID, serviceID, userID, channel)
	messages = make([]string, 0)
	key := fmt.Sprintf(consts.RedisChatMessages, tenantID, serviceID, userID, channel)
	if exist, err := g.Redis().Exists(ctx, key); err != nil {
		s.logger().Error(ctx, err)
	} else if exist == 1 {
		vMessages, err := g.Redis().Get(ctx, key)
		if err != nil {
			s.logger().Error(ctx, err)
		} else {
			messages = append(messages, vMessages.Strings()...)
		}
	}

	return
}
func (s *sDSH) updateCatchMessages(ctx context.Context, tenantID, serviceID, userID, channel string, messages []string) (err error) {
	key := fmt.Sprintf(consts.RedisChatMessages, tenantID, serviceID, userID, channel)
	return g.Redis().SetEX(ctx, key, messages, int64(gtime.D.Seconds()))
}

func (s *sDSH) GetChatMessages(ctx context.Context, tenantID, serviceID, userID, channel string) (messages []string, err error) {

	vDays, _ := g.Cfg().Get(ctx, "system.chat_message.recent_duration", "3d")
	catchMessages := s.getChatMessageFormCache(ctx, tenantID, serviceID, userID, channel)
	// 緩存中的資料會定期清除， 當沒有資料的情況下再從 db 中撈取數據並填寫到 catch
	if len(catchMessages) > 0 {

		messages = append(messages, catchMessages...)

		return
	}

	tableName := fmt.Sprintf(consts.TableChatMessagePattern, gtime.Now().Format("Y_m"))
	nowTime := gtime.Now()
	beforeTime := nowTime.Add(-vDays.Duration())

	req := model.GetContentsReq{
		Schema:    tenantID,
		Table:     tableName,
		WhereCond: "tenant_id = ? and service_id= ? and user_id = ? and create_at between ? and ? and channel= ?  ",
		Params:    g.Slice{tenantID, serviceID, userID, beforeTime, nowTime, channel},
		Fields:    g.Slice{"message", "role", "create_at"},
		Order:     "create_at desc",
	}

	var res *model.GetContentsRes
	err = s.sendRequest(ctx, consts.UriDSHGetContents, req, &res)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	if res.Code != consts.Success.Code() {
		err = gerror.New(res.Message)
		s.logger().Error(ctx, err)
		return
	}

	for _, content := range res.Contents {
		var msg *model.ChatMessage
		_ = gconv.Struct(content, &msg)
		if msg != nil {
			messages = append(messages, gjson.New(msg).MustToJsonString())
		}
	}

	_ = s.updateCatchMessages(ctx, tenantID, serviceID, userID, channel, messages)

	return

}

func (s *sDSH) InsertNewChatMessage(ctx context.Context, message *model.GenericMessage) (err error) {
	tableName := fmt.Sprintf(consts.TableChatMessage, gtime.Now().Format("Y_m"))
	mqMessage := &model.MQMessage{
		Schema: message.TenantID,
		Table:  tableName,
		Data:   message,
	}
	if err = s.sendMessage(ctx, consts.ActionInsert, []byte(mqMessage.String())); err != nil {
		s.logger().Error(ctx, err)
	}
	messages := s.getChatMessageFormCache(ctx, message.TenantID, message.ServiceID, message.UserID, message.Channel)
	if len(messages) > 0 {
		var msg = &model.ChatMessage{
			Role:     message.Role,
			Message:  message.Message,
			CreateAt: time.Now(),
		}

		messages = append([]string{gjson.New(msg).MustToJsonString()}, messages...)

		_ = s.updateCatchMessages(ctx, message.TenantID, message.ServiceID, message.UserID, message.Channel, messages)
	}
	return
}

func (s *sDSH) sendRequest(ctx context.Context, uri string, data any, res any) (err error) {
	vDSHName, err := g.Cfg().Get(ctx, "system.data_sync.name", "dsh.svc")
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	vScheme, err := g.Cfg().Get(ctx, "system.data_sync.scheme", "http")
	if err != nil {
		s.logger().Error(ctx, err)
	}

	url := fmt.Sprintf("%s://%s%s", vScheme.String(), vDSHName.String(), uri)
	s.logger().Debugf(ctx, "send to url=%v ,request=%v", url, gjson.New(data).MustToJsonIndentString())
	response, err := s.client.ContentJson().SetHeader(consts.XHeaderService, consts.ServiceName).Post(ctx, url, data)
	if err != nil {
		s.logger().Error(ctx, err)
		return err
	}
	defer func(response *gclient.Response) {
		_ = response.Close()
	}(response)

	strResponse := response.ReadAllString()

	s.logger().Debugf(ctx, "response=%v", strResponse)
	if !gjson.Valid(strResponse) {
		err = fmt.Errorf("invalid response: %v", strResponse)
		s.logger().Error(ctx, err)
		return err

	} else {
		err = gjson.New(strResponse).Scan(res)

	}

	return
}

func (s *sDSH) GetSystemInstruction(ctx context.Context, tenantID string) (sysInstruction *model.SystemInstruction, err error) {
	sysInstruction = s.getSysInstructionFromCatch(ctx, tenantID)
	if sysInstruction != nil {
		return
	}

	req := model.GetContentsReq{
		Schema:    consts.SchemaDSH,
		Table:     consts.TableTenantPrompt,
		WhereCond: "tenant_id = ?",
		Params:    g.Slice{tenantID},
	}

	var res = &model.GetContentsRes{}
	err = s.sendRequest(ctx, consts.UriDSHGetContents, req, &res)
	if err != nil {
		return nil, err
	}

	if res.Code != consts.Success.Code() {
		err = gerror.New(res.Message)
		return
	}

	if len(res.Contents) > 0 {
		err = gconv.Struct(res.Contents[0], &sysInstruction)
		// set data to catch
		_ = g.Redis().SetEX(ctx, fmt.Sprintf(consts.RedisKeyPrompt, tenantID), gjson.New(sysInstruction).MustToJsonString(), int64(gtime.D.Seconds()))
	} else {

		return nil, nil

	}

	return
}

func (s *sDSH) getSysInstructionFromCatch(ctx context.Context, tenantID string) (sysInstructions *model.SystemInstruction) {
	vSysInstruction, err := g.Redis().Get(ctx, fmt.Sprintf(consts.RedisKeyPrompt, tenantID))
	if err != nil {
		s.logger().Error(ctx, err)
		return nil
	}
	if vSysInstruction != nil && !vSysInstruction.IsNil() {
		_ = vSysInstruction.Struct(&sysInstructions)
		if sysInstructions != nil && !sysInstructions.IsEmpty() {
			return
		} else {
			return nil
		}
	}

	return nil
}

func (s *sDSH) GetLLMParams(ctx context.Context, tenantID string) (llmParams *model.LLMParams, err error) {
	s.logger().Debugf(ctx, "get llm params for tenant [%v]", tenantID)

	// 首先尝试从Redis缓存获取
	llmParams, err = s.getLLMParamsFromCache(ctx, tenantID)
	if err != nil {
		s.logger().Error(ctx, err)
	}
	if llmParams != nil {
		return llmParams, nil
	}

	// 缓存未命中，从数据库获取
	return s.getLLMParamsFromDB(ctx, tenantID)
}

func (s *sDSH) getLLMParamsFromCache(ctx context.Context, tenantID string) (*model.LLMParams, error) {
	redisKey := fmt.Sprintf(consts.RedisKeyTenantLLM, tenantID)
	isExist, err := g.Redis().Exists(ctx, redisKey)
	if err != nil {
		return nil, err
	}

	if isExist != 1 {
		return nil, nil
	}

	vLLMName, err := g.Redis().Get(ctx, redisKey)
	if err != nil {
		return nil, err
	}

	if vLLMName == nil || vLLMName.IsNil() {
		return nil, nil
	}

	llmName := s.extractLLMName(vLLMName)
	if g.IsEmpty(llmName) {
		return nil, nil
	}

	llmParamKey := fmt.Sprintf(consts.RedisLLMParams, llmName)
	vLLMParams, err := g.Redis().Get(ctx, llmParamKey)
	if err != nil {
		return nil, err
	}
	if vLLMParams == nil || vLLMParams.IsNil() {
		return nil, nil
	}

	var llmParams *model.LLMParams
	err = vLLMParams.Struct(&llmParams)
	return llmParams, err
}

func (s *sDSH) extractLLMName(vLLMName *gvar.Var) string {
	names := vLLMName.Strings()
	if len(names) > 0 {
		return names[0]
	}
	return ""
}

func (s *sDSH) getLLMParamsFromDB(ctx context.Context, tenantID string) (*model.LLMParams, error) {
	// 获取租户的LLM名称
	llmName, err := s.getLLMNameByTenant(ctx, tenantID)
	if err != nil {
		return nil, err
	}

	// 根据LLM名称获取参数
	return s.getLLMParamsByName(ctx, llmName)
}

func (s *sDSH) getLLMNameByTenant(ctx context.Context, tenantID string) (string, error) {
	req := model.GetContentsReq{
		Schema:    consts.SchemaDSH,
		Table:     consts.TableTenantLLM,
		WhereCond: "tenant_id = ?",
		Params:    g.Slice{tenantID},
		Fields:    g.Slice{"llm_name"},
	}

	var resLLM = &model.GetContentsRes{}
	err := s.sendRequest(ctx, consts.UriDSHGetContents, req, &resLLM)
	if err != nil {
		return "", err
	}

	if resLLM.Code != consts.Success.Code() {
		return "", gerror.New(resLLM.Message)
	}

	if len(resLLM.Contents) == 0 {
		return "", gerror.New("not found llm params")
	}

	return gconv.String(resLLM.Contents[0]["llm_name"]), nil
}

func (s *sDSH) getLLMParamsByName(ctx context.Context, llmName string) (*model.LLMParams, error) {
	request := model.GetContentsReq{
		Schema:    consts.SchemaDSH,
		Table:     consts.TableLLMParams,
		WhereCond: "llm_name = ?",
		Params:    g.Slice{llmName},
	}

	var resParams = &model.GetContentsRes{}
	err := s.sendRequest(ctx, consts.UriDSHGetContents, request, &resParams)
	if err != nil {
		return nil, err
	}

	if resParams.Code != consts.Success.Code() {
		return nil, gerror.New(resParams.Message)
	}

	if len(resParams.Contents) == 0 {
		return nil, gerror.Newf("not found llm params for llm [%v]", llmName)
	}

	var llmParams *model.LLMParams
	err = gconv.Struct(resParams.Contents[0], &llmParams)
	return llmParams, err
}
