package llmRouter

import (
	"brainHub/internal/consts"
	"brainHub/internal/llms"
	"brainHub/internal/model"
	"brainHub/internal/model/llm"
	"brainHub/internal/model/tenant"
	"brainHub/internal/service"
	"brainHub/utility"
	"context"

	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
)

func init() {
	service.RegisterAiRouter(New())
}

type sAiRouter struct {
	keyID2Params *gmap.StrAnyMap
}

func New() service.IAiRouter {

	return &sAiRouter{
		keyID2Params: gmap.NewStrAnyMap(true),
	}

}
func (s *sAiRouter) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogRouter)
}

// RemoveKeyParams removes the specified key and its associated parameters from the key-to-parameters mapping.
func (s *sAiRouter) RemoveKeyParams(ctx context.Context, key string) {
	s.logger().Infof(ctx, "remove key params : %v", key)
	s.keyID2Params.Remove(key)
}
func (s *sAiRouter) getPayload(ctx context.Context, tenantID, serviceID, userID, channel string) (payload *llm.Payload, err error) {
	if tenantID == serviceID {
		s.logger().Warning(ctx, "the tenant id is equal to service id")
		return &llm.Payload{
			Attachments: &model.Asset{
				YoutubeLink:  []string{},
				PlainText:    []string{},
				WebPageFiles: []string{},
				Files:        []string{},
			},
			History: []string{},
		}, nil
	}
	// the key format: tenantID : serviceID .If this format is not the case, you cannot get the payload
	asset, err := service.AMS().GetResources(ctx, tenantID, serviceID)

	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	historyMessages, _ := service.DSH().GetChatMessages(ctx, tenantID, serviceID, userID, channel)

	payload = &llm.Payload{
		Attachments: asset,
		History:     historyMessages,
	}

	return

}
func (s *sAiRouter) getSystemInstruction(ctx context.Context, tenantID string, serviceID string, channelType string) (sysInstruction string, err error) {
	sysInstructionPrompts, err := service.DSH().GetSystemInstruction(ctx, tenantID)
	if err != nil {

		return
	}
	if sysInstructionPrompts == nil {
		s.logger().Debug(ctx, "not found system instruction")
		return
	}
	sysInstruction = sysInstructionPrompts.System

	for _, instruction := range sysInstructionPrompts.ServiceInstructions {
		if instruction.ServiceID == serviceID && instruction.Channel == channelType {
			sysInstruction = instruction.SysInstruction
			return
		}

	}
	nowTime := gtime.Now()
	sysInstruction = gstr.Replace(sysInstruction, "{{.now_date}}", nowTime.Format("Y-m-d H:i:s"))

	return
}
func (s *sAiRouter) getLLMConfig(ctx context.Context, llmParams *model.LLMParams, tenantParams g.Map) (cfg *llm.LLMsConfig) {
	var vLLMCfg *g.Var
	var err error
	vLLMCfg, err = g.Cfg().Get(ctx, "default_llms")

	if err != nil {
		s.logger().Error(ctx, err)
	}

	var defaultLLMs *llm.LLMsConfig
	_ = vLLMCfg.Struct(&defaultLLMs)

	// 如果 llmParams 為 nil，直接返回 defaultLLMs
	if llmParams == nil {
		return defaultLLMs
	}

	switch gstr.ToLower(llmParams.LLMType) {
	default:
		cfg = defaultLLMs
	case consts.VertexAIGemini:
		cfg, err = utility.ConvertLLMParamToGeminiParams(llmParams, tenantParams)
		if err != nil {
			s.logger().Error(ctx, err)
			cfg = defaultLLMs
		}

	case consts.AOAI:
		cfg = &llm.LLMsConfig{
			AoAi: llmParams,
		}
	case consts.VertexAIClaude:
		cfg, err = utility.ConvertLLMParamsToVertexParams(llmParams, tenantParams)
		if err != nil {
			s.logger().Error(ctx, err)
			cfg = defaultLLMs
		}

	}

	return

}

// Select retrieves or initializes an AI model instance associated with the given tenant key and user ID.
// If the key exists in the cache, it retrieves the corresponding configuration to initialize the model.
// Otherwise, it creates a default model configuration for the tenant and initializes the model.

func (s *sAiRouter) Select(ctx context.Context, in *model.AiSelectorInput) (llmObj llms.ILLMs, err error) {

	s.logger().Debugf(ctx, " select llm for tenant [%v] , service [%v] , user [%v] , channel [%v]",
		in.TenantID, in.ServiceID, in.UserID, in.Channel)

	llmParams, err := service.DSH().GetLLMParams(ctx, in.TenantID)

	if err != nil {
		s.logger().Error(ctx, err)

	}

	// get prompt
	systemInstruction, err := s.getSystemInstruction(ctx, in.TenantID, in.ServiceID, in.Channel)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	var payload *llm.Payload

	// 用於追蹤是否需要調用 Initialize 方法
	var shouldInitialize bool = true

	if s.keyID2Params.Contains(in.TenantID) {
		var tenantParams *tenant.AiModelConfig
		_ = s.keyID2Params.GetVar(in.TenantID).Struct(&tenantParams)

		if tenantParams != nil {
			needPayload := false
			llmObj, needPayload, err = tenantParams.LLM(in.UserID)
			if err != nil {
				s.logger().Error(ctx, err)
				return
			}

			// 根據 needPayload 判斷是否為新建立的物件
			if needPayload {
				// 新建立的物件，需要獲取 payload 並調用 Initialize
				payload, err = s.getPayload(ctx, in.TenantID, in.ServiceID, in.UserID, in.Channel)
				if err != nil {
					s.logger().Error(ctx, err)
					return
				}
				shouldInitialize = true
				s.logger().Debug(ctx, "New LLM instance created, will call Initialize")
			} else {
				// 從緩存中取出的物件，跳過 Initialize 調用
				shouldInitialize = false
				s.logger().Debug(ctx, "LLM instance retrieved from cache, skipping Initialize")
			}

		} else {
			err = gerror.Newf("Can not found tenant [%v] params ", in.TenantID)
			return
		}
	} else {
		// 创建tenant 对应的 model 对象。
		//初期为了 demo 先省去为了 tenant 配置模型的动作统一使用 default ai 模型的配置
		// 先获取 payload  判断website 资料是否抓取完成如果没有完成直接返回 error

		payload, err = s.getPayload(ctx, in.TenantID, in.ServiceID, in.UserID, in.Channel)

		if err != nil {
			return
		}
		// get default AI if the AI model is not set
		var vDefaultModelType *g.Var
		vDefaultModelType, err = g.Cfg().Get(ctx, "system.default_ai")

		if err != nil {
			s.logger().Error(ctx, err)

		}
		modelType := vDefaultModelType.String()
		if llmParams != nil {
			modelType = llmParams.LLMType
		}

		var tenantParams = &tenant.AiModelConfig{
			TenantID:  in.TenantID,
			ModelType: modelType,
		}

		_ = tenantParams.Create(ctx)
		s.keyID2Params.Set(in.TenantID, tenantParams)
		llmObj, _, err = tenantParams.LLM(in.UserID)

		// 新租戶的 LLM 物件需要初始化
		shouldInitialize = true
		s.logger().Debug(ctx, "New tenant LLM instance created, will call Initialize")

	}

	if llmObj != nil && shouldInitialize {
		// 只有在需要初始化時才調用 Initialize 方法
		if payload != nil {
			payload.SystemInstruction = systemInstruction
		}
		llmsConfig := s.getLLMConfig(ctx, llmParams, g.Map{"tenant_id": in.TenantID})
		s.logger().Debug(ctx, "Called Initialize method for new LLM instance")

		err = llmObj.Initialize(ctx, llmsConfig, payload)

	} else if llmObj != nil {
		s.logger().Debug(ctx, "Skipped Initialize method for cached LLM instance")
	}

	return llmObj, err
}
