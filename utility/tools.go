package utility

import (
	lineChat "brainHub/api/brain/v1"
	omniChat "brainHub/api/omnichannel/v1"
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"brainHub/internal/model/llm"
	"context"
	"fmt"
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gproc"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/kkdai/youtube/v2"
)

func GetYTMime(ctx context.Context, url string) string {
	client := youtube.Client{}
	video, err := client.GetVideo(url)
	if err != nil {
		g.Log().Error(ctx, err)
		return ""
	}
	for _, format := range video.Formats {
		if gstr.HasPrefix(format.MimeType, "video/") {
			ary := gstr.SplitAndTrim(format.MimeType, ";")
			if len(ary) > 0 {
				return ary[0]
			}

		}
	}
	return ""
}
func ConvertDocToMDToCountToken(ctx context.Context, fullFileName string) bool {
	bOverSeed := false
	fnCountFileContent := func(mdFile string) {
		if gfile.Exists(mdFile) {
			wordCount := 0
			_ = gfile.ReadLines(mdFile, func(line string) error {
				wordCount += gstr.LenRune(line)
				return nil
			})
			bOverSeed = wordCount > consts.MaxFileToken
		}

	}

	if gstr.ToLower(gfile.ExtName(fullFileName)) == "md" {
		fnCountFileContent(fullFileName)
		return bOverSeed

	}

	if g.IsEmpty(gproc.SearchBinary("markitdown")) {
		return bOverSeed
	} else {

		mdFullFileName := gfile.Join(gfile.Dir(fullFileName), gfile.Name(fullFileName)+".md")
		cmd := fmt.Sprintf("markitdown %q -o %q", fullFileName, mdFullFileName)
		g.Log().Debugf(ctx, "cmd: %s", cmd)
		_, err := gproc.ShellExec(ctx, cmd)

		if err != nil {
			g.Log().Error(ctx, err)
			return bOverSeed
		} else {
			fnCountFileContent(mdFullFileName)
		}
		defer func() {
			_ = gfile.RemoveFile(mdFullFileName)
		}()

	}
	return bOverSeed

}

// ConvertFileToMarkdown 將文件轉換為 Markdown 格式
// 如果文件不是文本或圖片格式，且系統中存在 markitdown 工具，則進行轉換
//
// 參數:
//   - ctx: 上下文
//   - filePath: 原始文件路徑
//   - mimeType: 文件的 MIME 類型
//
// 返回:
//   - convertedPath: 轉換後的文件路徑（如果轉換成功）或原始文件路徑
//   - isConverted: 是否進行了轉換
//   - shouldSkip: 是否應該跳過此文件（轉換失敗時）
//   - err: 轉換過程中的錯誤
func ConvertFileToMarkdown(ctx context.Context, filePath, mimeType string) (convertedPath string, isConverted bool, shouldSkip bool, err error) {
	// 檢查文件是否存在
	if !gfile.Exists(filePath) {
		err = gerror.Newf("file does not exist: %s", filePath)
		g.Log().Error(ctx, "ConvertFileToMarkdown file check failed:", err)
		return "", false, true, err
	}

	// 檢查是否為文本文件，如果是則直接返回（包括 markdown）
	if isTextFile(mimeType) {
		g.Log().Debugf(ctx, "File is text format, no conversion needed: %s (MIME: %s)", filePath, mimeType)
		return filePath, false, false, nil
	}

	// 檢查是否為圖片文件，如果是則直接返回（圖片不需要轉換）
	if isImageFile(mimeType) {
		g.Log().Debugf(ctx, "File is image format, no conversion needed: %s (MIME: %s)", filePath, mimeType)
		return filePath, false, false, nil
	}

	// 檢查系統中是否存在 markitdown 工具
	if g.IsEmpty(gproc.SearchBinary("markitdown")) {
		g.Log().Warningf(ctx, "markitdown tool not found, skipping file conversion: %s", filePath)
		return "", false, true, gerror.New("markitdown tool not available")
	}

	// 生成轉換後的文件路徑
	mdFileName := gfile.Name(filePath) + ".md"
	mdFilePath := gfile.Join(gfile.Dir(filePath), mdFileName)
	// 如md 的文件已經存在則無需要轉換直接返回
	if gfile.Exists(mdFilePath) {
		g.Log().Warningf(ctx, "md file already exists, no conversion needed: %s", mdFilePath)
		return mdFilePath, false, false, nil
	}

	// 執行 markitdown 轉換命令
	cmd := fmt.Sprintf("markitdown %q -o %q", filePath, mdFilePath)
	g.Log().Debugf(ctx, "Executing markitdown conversion: %s", cmd)

	_, err = gproc.ShellExec(ctx, cmd)
	if err != nil {
		g.Log().Errorf(ctx, "markitdown conversion failed for file %s (MIME: %s), command: %s, error: %v",
			filePath, mimeType, cmd, err)
		return "", false, true, err
	}

	// 檢查轉換後的文件是否存在且不為空
	if !gfile.Exists(mdFilePath) || gfile.Size(mdFilePath) == 0 {
		g.Log().Errorf(ctx, "markitdown conversion produced empty or missing output file: %s -> %s", filePath, mdFilePath)
		return "", false, true, gerror.New("conversion produced empty or missing file")
	}

	g.Log().Infof(ctx, "Successfully converted file to markdown: %s -> %s (MIME: %s)", filePath, mdFilePath, mimeType)
	return mdFilePath, true, false, nil
}

// isTextFile 檢查 MIME 類型是否為文本格式
// 所有以 text/ 開頭的 MIME 類型都被視為文本文件，不需要轉換
func isTextFile(mimeType string) bool {
	return gstr.HasPrefix(mimeType, "text/")
}

// isImageFile 檢查 MIME 類型是否為圖片格式
func isImageFile(mimeType string) bool {
	imageTypes := []string{
		"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp",
		"image/bmp", "image/tiff", "image/svg+xml", "image/heic", "image/heif",
	}

	for _, imageType := range imageTypes {
		if mimeType == imageType {
			return true
		}
	}
	return false
}
func ParseJsonFormatResponse(generateResponse string) string {
	// 只移除首尾的空白字符，保留內容中的空格
	var str = gstr.Trim(generateResponse)
	if gstr.ContainsI(str, "```json") {
		str = gstr.SubStrFrom(str, "```json")
		str = gstr.TrimLeftStr(str, "```json\n")
		str = gstr.TrimLeftStr(str, "```json")
		str = gstr.TrimRightStr(str, "\n```")
		str = gstr.TrimRightStr(str, "```")
		// 再次移除首尾空白字符，但保留內容中的空格
		str = gstr.Trim(str)
	}

	return str
}
func RemoveFiles(pathName string) {

	if gfile.Exists(pathName) {
		_ = gfile.RemoveAll(pathName)
	}
}

// ConvertLLMParamToGeminiParams 將LLM參數轉換為Gemini參數配置
//
// 參數:
//   - llmParam: LLM參數模型，包含基本配置信息
//   - tenantParams: 租戶參數映射，用於替換特定配置中的租戶ID
//
// 返回:
//   - vertexParam: 轉換後的Gemini配置參數
//   - err: 處理過程中可能發生的錯誤
//
// 功能說明:
//
//	此函數將通用LLM參數轉換為Gemini特定配置，處理區域、項目ID、
//	溫度、令牌限制等關鍵參數，並支援租戶ID的動態替換
func ConvertLLMParamToGeminiParams(llmParam *model.LLMParams, tenantParams g.Map) (vertexParam *llm.LLMsConfig, err error) {
	if llmParam == nil {
		return nil, gerror.New("the llm param is nil")
	}
	if llmParam.LLMType != consts.VertexAIGemini {
		return nil, gerror.Newf("the llm type [%v] is not supported", llmParam.LLMType)
	}

	region := gjson.New(llmParam.BaseUrl).Get("region").String()
	if g.IsEmpty(region) {
		return nil, gerror.New("the region is empty")
	}

	projectID := gjson.New(llmParam.BaseUrl).Get("project_id").String()
	if g.IsEmpty(projectID) {
		return nil, gerror.New("the project id is empty")
	}

	if gstr.ToLower(projectID) == "$tenant_id" {
		projectID = gconv.String(tenantParams["tenant_id"])
	}
	includeThoughts := false

	includeThoughts = gjson.New(llmParam.BaseUrl).Get("include_thoughts").Bool()
	thinkingBudget := gjson.New(llmParam.BaseUrl).Get("thinking_budget").Int32()

	vertexParam = &llm.LLMsConfig{}
	vertexParam.Vertex.LLMType = consts.VertexAIGemini
	vertexParam.Common.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)
	vertexParam.Common.Temperature = llmParam.Temperature
	vertexParam.Vertex.Region = region
	vertexParam.Vertex.ProjectID = projectID
	vertexParam.Vertex.CredentialFile = projectID + ".json"
	vertexParam.Vertex.Gemini.Model = llmParam.ModelId
	vertexParam.Vertex.Gemini.Temperature = llmParam.Temperature
	vertexParam.Vertex.Gemini.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)

	vertexParam.Vertex.Gemini.IncludeThoughts = includeThoughts
	vertexParam.Vertex.Gemini.ThinkingBudget = thinkingBudget

	return vertexParam, nil
}

func ConvertLLMParamsToVertexParams(llmParam *model.LLMParams, tenantParams g.Map) (vertexParam *llm.LLMsConfig, err error) {
	if llmParam == nil {
		return nil, gerror.New("the llm param is nil")
	}
	if llmParam.LLMType != consts.VertexAIClaude {
		return nil, gerror.Newf("the llm type [%v] is not supported", llmParam.LLMType)
	}

	region := gjson.New(llmParam.BaseUrl).Get("region").String()
	if g.IsEmpty(region) {
		return nil, gerror.New("the region is empty")
	}

	projectID := gjson.New(llmParam.BaseUrl).Get("project_id").String()
	if g.IsEmpty(projectID) {
		return nil, gerror.New("the project id is empty")
	}

	if gstr.ToLower(projectID) == "$tenant_id" {
		projectID = gconv.String(tenantParams["tenant_id"])
	}

	vertexParam = &llm.LLMsConfig{}
	vertexParam.Vertex.LLMType = consts.VertexAIClaude
	vertexParam.Common.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)
	vertexParam.Common.Temperature = llmParam.Temperature
	vertexParam.Vertex.Region = region
	vertexParam.Vertex.ProjectID = projectID
	vertexParam.Vertex.CredentialFile = projectID + ".json"

	vertexParam.Vertex.ThirdModel.Model = llmParam.ModelId
	vertexParam.Vertex.ThirdModel.Temperature = llmParam.Temperature
	vertexParam.Vertex.ThirdModel.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)
	vertexParam.Vertex.ThirdModel.APIVersion = llmParam.APIVersion

	return
}

func ConvertAnyToGenericMessage(data any) *model.GenericMessage {
	nowTime := time.Now()
	var message = &model.GenericMessage{
		CrateAt: nowTime,
	}
	switch v := data.(type) {
	default:
		return nil

	case *lineChat.ChatReq:
		message.Message = v.Message
		message.ServiceID = v.ServiceID
		message.UserID = v.UserID
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentTypeText
		message.Channel = v.Channel
		message.Role = consts.RoleUser
		message.DisplayName = v.DisplayName

	case *lineChat.ChatWithAttachmentReq:
		message.ServiceID = v.ServiceID
		message.UserID = v.UserID
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentMediaFile
		message.Channel = v.Channel
		message.Role = consts.RoleUser
	case *omniChat.ChatReq:
		message.Message = v.Question
		message.ServiceID = v.ServiceId
		message.UserID = v.UserId
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentTypeText
		message.Channel = v.Channel
		message.Role = consts.RoleUser

	case *llm.ResponseData:
		message.Message = gconv.String(v.Response)
		message.ServiceID = v.ServiceID
		message.UserID = v.UserID
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentTypeText
		message.Channel = v.Channel
		message.Role = consts.RoleAi
	}

	return message
}

// LogGenerateContent 記錄內容生成的詳細日誌
//
// 參數:
//   - ctx: 上下文
//   - tenantID: 租戶ID
//   - serviceID: 服務ID
//   - channel: 渠道標識
//   - response: 生成響應數據
//
// 功能說明:
//
//	記錄內容生成的關鍵信息，包括模型名稱、Token使用量、
//	生成時間、續寫次數等，便於監控和分析
func LogGenerateContent(ctx context.Context, tenantID, serviceID, channel string, response *llm.GenerateContentResponse) {
	if response == nil {
		g.Log().Warning(ctx, "LogGenerateContent: response is nil")
		return
	}

	// 記錄詳細的生成信息
	g.Log().Infof(ctx,
		"GenerateContent completed - tenant_id=%s, service_id=%s, channel=%s, model=%s, input_tokens=%d, output_tokens=%d, total_tokens=%d, continuations=%d, complete=%v, duration=%dms",
		tenantID, serviceID, channel, response.LLMName,
		response.InputTokens, response.OutputTokens, response.TotalTokens,
		response.ContinuationCount, response.IsComplete, response.GenerationTime)

	// 如果有安全警告，單獨記錄
	if len(response.SafetyWarnings) > 0 {
		g.Log().Warningf(ctx,
			"GenerateContent safety warnings - tenant_id=%s, service_id=%s, warnings=%v",
			tenantID, serviceID, response.SafetyWarnings)
	}

	// 如果內容不完整，記錄警告
	if !response.IsComplete {
		g.Log().Warningf(ctx,
			"GenerateContent incomplete - tenant_id=%s, service_id=%s, continuations=%d, reason=%s",
			tenantID, serviceID, response.ContinuationCount, response.FinishReason)
	}

	// 記錄思考過程（如果有）
	if response.ThinkingProcess != "" {
		g.Log().Debugf(ctx,
			"GenerateContent thinking process - tenant_id=%s, service_id=%s, thinking_length=%d",
			tenantID, serviceID, len(response.ThinkingProcess))
	}
}
