package utility

import (
	lineChat "brainHub/api/brain/v1"
	omniChat "brainHub/api/omnichannel/v1"
	"brainHub/internal/consts"
	"brainHub/internal/model"
	"brainHub/internal/model/llm"
	"context"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gproc"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/gogf/gf/v2/util/grand"
	"github.com/kkdai/youtube/v2"
)

func GetYTMime(ctx context.Context, url string) string {
	client := youtube.Client{}
	video, err := client.GetVideo(url)
	if err != nil {
		g.Log().Error(ctx, err)
		return ""
	}
	for _, format := range video.Formats {
		if gstr.HasPrefix(format.MimeType, "video/") {
			ary := gstr.SplitAndTrim(format.MimeType, ";")
			if len(ary) > 0 {
				return ary[0]
			}

		}
	}
	return ""
}
func ConvertDocToMDToCountToken(ctx context.Context, fullFileName string) bool {
	bOverSeed := false
	fnCountFileContent := func(mdFile string) {
		if gfile.Exists(mdFile) {
			wordCount := 0
			_ = gfile.ReadLines(mdFile, func(line string) error {
				wordCount += gstr.LenRune(line)
				return nil
			})
			bOverSeed = wordCount > consts.MaxFileToken
		}

	}

	if gstr.ToLower(gfile.ExtName(fullFileName)) == "md" {
		fnCountFileContent(fullFileName)
		return bOverSeed

	}

	if g.IsEmpty(gproc.SearchBinary("markitdown")) {
		return bOverSeed
	} else {

		mdFullFileName := gfile.Join(gfile.Dir(fullFileName), gfile.Name(fullFileName)+".md")
		cmd := fmt.Sprintf("markitdown %q -o %q", fullFileName, mdFullFileName)
		g.Log().Debugf(ctx, "cmd: %s", cmd)
		_, err := gproc.ShellExec(ctx, cmd)

		if err != nil {
			g.Log().Error(ctx, err)
			return bOverSeed
		} else {
			fnCountFileContent(mdFullFileName)
		}
		defer func() {
			_ = gfile.RemoveFile(mdFullFileName)
		}()

	}
	return bOverSeed

}

// ConvertFileToMarkdown 將文件轉換為 Markdown 格式
// 如果文件不是文本或圖片格式，且系統中存在 markitdown 工具，則進行轉換
//
// 參數:
//   - ctx: 上下文
//   - filePath: 原始文件路徑
//   - mimeType: 文件的 MIME 類型
//
// 返回:
//   - convertedPath: 轉換後的文件路徑（如果轉換成功）或原始文件路徑
//   - isConverted: 是否進行了轉換
//   - shouldSkip: 是否應該跳過此文件（轉換失敗時）
//   - err: 轉換過程中的錯誤
func ConvertFileToMarkdown(ctx context.Context, filePath, mimeType string) (convertedPath string, isConverted bool, shouldSkip bool, err error) {
	// 檢查文件是否存在
	if !gfile.Exists(filePath) {
		err = gerror.Newf("file does not exist: %s", filePath)
		g.Log().Error(ctx, "ConvertFileToMarkdown file check failed:", err)
		return "", false, true, err
	}

	// 檢查是否為文本文件，如果是則直接返回（包括 markdown）
	if isTextFile(mimeType) {
		g.Log().Debugf(ctx, "File is text format, no conversion needed: %s (MIME: %s)", filePath, mimeType)
		return filePath, false, false, nil
	}

	// 檢查是否為圖片文件，如果是則直接返回（圖片不需要轉換）
	if isImageFile(mimeType) {
		g.Log().Debugf(ctx, "File is image format, no conversion needed: %s (MIME: %s)", filePath, mimeType)
		return filePath, false, false, nil
	}

	// 檢查系統中是否存在 markitdown 工具
	if g.IsEmpty(gproc.SearchBinary("markitdown")) {
		g.Log().Warningf(ctx, "markitdown tool not found, skipping file conversion: %s", filePath)
		return "", false, true, gerror.New("markitdown tool not available")
	}

	// 生成轉換後的文件路徑
	mdFileName := gfile.Name(filePath) + ".md"
	mdFilePath := gfile.Join(gfile.Dir(filePath), mdFileName)
	// 如md 的文件已經存在則無需要轉換直接返回
	if gfile.Exists(mdFilePath) {
		g.Log().Warningf(ctx, "md file already exists, no conversion needed: %s", mdFilePath)
		return mdFilePath, false, false, nil
	}

	// 執行 markitdown 轉換命令
	cmd := fmt.Sprintf("markitdown %q -o %q", filePath, mdFilePath)
	g.Log().Debugf(ctx, "Executing markitdown conversion: %s", cmd)

	_, err = gproc.ShellExec(ctx, cmd)
	if err != nil {
		g.Log().Errorf(ctx, "markitdown conversion failed for file %s (MIME: %s), command: %s, error: %v",
			filePath, mimeType, cmd, err)
		return "", false, true, err
	}

	// 檢查轉換後的文件是否存在且不為空
	if !gfile.Exists(mdFilePath) || gfile.Size(mdFilePath) == 0 {
		g.Log().Errorf(ctx, "markitdown conversion produced empty or missing output file: %s -> %s", filePath, mdFilePath)
		return "", false, true, gerror.New("conversion produced empty or missing file")
	}

	g.Log().Infof(ctx, "Successfully converted file to markdown: %s -> %s (MIME: %s)", filePath, mdFilePath, mimeType)
	return mdFilePath, true, false, nil
}

// isTextFile 檢查 MIME 類型是否為文本格式
// 所有以 text/ 開頭的 MIME 類型都被視為文本文件，不需要轉換
func isTextFile(mimeType string) bool {
	return gstr.HasPrefix(mimeType, "text/")
}

// isImageFile 檢查 MIME 類型是否為圖片格式
func isImageFile(mimeType string) bool {
	imageTypes := []string{
		"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp",
		"image/bmp", "image/tiff", "image/svg+xml", "image/heic", "image/heif",
	}

	for _, imageType := range imageTypes {
		if mimeType == imageType {
			return true
		}
	}
	return false
}
func ParseJsonFormatResponse(generateResponse string) string {
	// 只移除首尾的空白字符，保留內容中的空格
	var str = gstr.Trim(generateResponse)
	if gstr.ContainsI(str, "```json") {
		str = gstr.SubStrFrom(str, "```json")
		str = gstr.TrimLeftStr(str, "```json\n")
		str = gstr.TrimLeftStr(str, "```json")
		str = gstr.TrimRightStr(str, "\n```")
		str = gstr.TrimRightStr(str, "```")
		// 再次移除首尾空白字符，但保留內容中的空格
		str = gstr.Trim(str)
	}

	return str
}
func RemoveFiles(pathName string) {

	if gfile.Exists(pathName) {
		_ = gfile.RemoveAll(pathName)
	}
}

// ConvertLLMParamToGeminiParams 將LLM參數轉換為Gemini參數配置
//
// 參數:
//   - llmParam: LLM參數模型，包含基本配置信息
//   - tenantParams: 租戶參數映射，用於替換特定配置中的租戶ID
//
// 返回:
//   - vertexParam: 轉換後的Gemini配置參數
//   - err: 處理過程中可能發生的錯誤
//
// 功能說明:
//
//	此函數將通用LLM參數轉換為Gemini特定配置，處理區域、項目ID、
//	溫度、令牌限制等關鍵參數，並支援租戶ID的動態替換
func ConvertLLMParamToGeminiParams(llmParam *model.LLMParams, tenantParams g.Map) (vertexParam *llm.LLMsConfig, err error) {
	if llmParam == nil {
		return nil, gerror.New("the llm param is nil")
	}
	if llmParam.LLMType != consts.VertexAIGemini {
		return nil, gerror.Newf("the llm type [%v] is not supported", llmParam.LLMType)
	}

	region := gjson.New(llmParam.BaseUrl).Get("region").String()
	if g.IsEmpty(region) {
		return nil, gerror.New("the region is empty")
	}

	projectID := gjson.New(llmParam.BaseUrl).Get("project_id").String()
	if g.IsEmpty(projectID) {
		return nil, gerror.New("the project id is empty")
	}

	if gstr.ToLower(projectID) == "$tenant_id" {
		projectID = gconv.String(tenantParams["tenant_id"])
	}
	includeThoughts := false

	includeThoughts = gjson.New(llmParam.BaseUrl).Get("include_thoughts").Bool()
	thinkingBudget := gjson.New(llmParam.BaseUrl).Get("thinking_budget").Int32()

	vertexParam = &llm.LLMsConfig{}
	vertexParam.Vertex.LLMType = consts.VertexAIGemini
	vertexParam.Common.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)
	vertexParam.Common.Temperature = llmParam.Temperature
	vertexParam.Vertex.Region = region
	vertexParam.Vertex.ProjectID = projectID
	vertexParam.Vertex.CredentialFile = projectID + ".json"
	vertexParam.Vertex.Gemini.Model = llmParam.ModelId
	vertexParam.Vertex.Gemini.Temperature = llmParam.Temperature
	vertexParam.Vertex.Gemini.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)

	vertexParam.Vertex.Gemini.IncludeThoughts = includeThoughts
	vertexParam.Vertex.Gemini.ThinkingBudget = thinkingBudget

	return vertexParam, nil
}

func ConvertLLMParamsToVertexParams(llmParam *model.LLMParams, tenantParams g.Map) (vertexParam *llm.LLMsConfig, err error) {
	if llmParam == nil {
		return nil, gerror.New("the llm param is nil")
	}
	if llmParam.LLMType != consts.VertexAIClaude {
		return nil, gerror.Newf("the llm type [%v] is not supported", llmParam.LLMType)
	}

	region := gjson.New(llmParam.BaseUrl).Get("region").String()
	if g.IsEmpty(region) {
		return nil, gerror.New("the region is empty")
	}

	projectID := gjson.New(llmParam.BaseUrl).Get("project_id").String()
	if g.IsEmpty(projectID) {
		return nil, gerror.New("the project id is empty")
	}

	if gstr.ToLower(projectID) == "$tenant_id" {
		projectID = gconv.String(tenantParams["tenant_id"])
	}

	vertexParam = &llm.LLMsConfig{}
	vertexParam.Vertex.LLMType = consts.VertexAIClaude
	vertexParam.Common.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)
	vertexParam.Common.Temperature = llmParam.Temperature
	vertexParam.Vertex.Region = region
	vertexParam.Vertex.ProjectID = projectID
	vertexParam.Vertex.CredentialFile = projectID + ".json"

	vertexParam.Vertex.ThirdModel.Model = llmParam.ModelId
	vertexParam.Vertex.ThirdModel.Temperature = llmParam.Temperature
	vertexParam.Vertex.ThirdModel.MaxOutputTokens = gconv.Int32(llmParam.MaxToken)
	vertexParam.Vertex.ThirdModel.APIVersion = llmParam.APIVersion

	return
}

func ConvertAnyToGenericMessage(data any) *model.GenericMessage {
	nowTime := time.Now()
	var message = &model.GenericMessage{
		CrateAt: nowTime,
	}
	switch v := data.(type) {
	default:
		return nil

	case *lineChat.ChatReq:
		message.Message = v.Message
		message.ServiceID = v.ServiceID
		message.UserID = v.UserID
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentTypeText
		message.Channel = v.Channel
		message.Role = consts.RoleUser
		message.DisplayName = v.DisplayName

	case *lineChat.ChatWithAttachmentReq:
		message.ServiceID = v.ServiceID
		message.UserID = v.UserID
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentMediaFile
		message.Channel = v.Channel
		message.Role = consts.RoleUser
	case *omniChat.ChatReq:
		message.Message = v.Question
		message.ServiceID = v.ServiceId
		message.UserID = v.UserId
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentTypeText
		message.Channel = v.Channel
		message.Role = consts.RoleUser

	case *llm.ResponseData:
		message.Message = gconv.String(v.Response)
		message.ServiceID = v.ServiceID
		message.UserID = v.UserID
		message.TenantID = v.TenantID
		message.MessageType = consts.ContentTypeText
		message.Channel = v.Channel
		message.Role = consts.RoleAi
	}

	return message
}

// LogGenerateContent 記錄內容生成的詳細日誌
//
// 參數:
//   - ctx: 上下文
//   - tenantID: 租戶ID
//   - serviceID: 服務ID
//   - channel: 渠道標識
//   - response: 生成響應數據
//
// 功能說明:
//
//	記錄內容生成的關鍵信息，包括模型名稱、Token使用量、
//	生成時間、續寫次數等，便於監控和分析
func LogGenerateContent(ctx context.Context, tenantID, serviceID, channel string, response *llm.GenerateContentResponse) {
	if response == nil {
		g.Log().Warning(ctx, "LogGenerateContent: response is nil")
		return
	}

	// 記錄詳細的生成信息
	g.Log().Infof(ctx,
		"GenerateContent completed - tenant_id=%s, service_id=%s, channel=%s, model=%s, input_tokens=%d, output_tokens=%d, total_tokens=%d, continuations=%d, complete=%v, duration=%dms",
		tenantID, serviceID, channel, response.LLMName,
		response.InputTokens, response.OutputTokens, response.TotalTokens,
		response.ContinuationCount, response.IsComplete, response.GenerationTime)

	// 如果有安全警告，單獨記錄
	if len(response.SafetyWarnings) > 0 {
		g.Log().Warningf(ctx,
			"GenerateContent safety warnings - tenant_id=%s, service_id=%s, warnings=%v",
			tenantID, serviceID, response.SafetyWarnings)
	}

	// 如果內容不完整，記錄警告
	if !response.IsComplete {
		g.Log().Warningf(ctx,
			"GenerateContent incomplete - tenant_id=%s, service_id=%s, continuations=%d, reason=%s",
			tenantID, serviceID, response.ContinuationCount, response.FinishReason)
	}

	// 記錄思考過程（如果有）
	if response.ThinkingProcess != "" {
		g.Log().Debugf(ctx,
			"GenerateContent thinking process - tenant_id=%s, service_id=%s, thinking_length=%d",
			tenantID, serviceID, len(response.ThinkingProcess))
	}
}

// DownloadFileFromURL 從 URL 下載文件到臨時目錄
// 支援 voice 和 image 類型文件的下載，並返回本地文件路徑和 MIME 類型
//
// 參數:
//   - ctx: 上下文
//   - url: 文件下載 URL
//   - messageType: 消息類型 (voice, image)
//
// 返回:
//   - localPath: 下載後的本地文件路徑
//   - mimeType: 檢測到的 MIME 類型
//   - err: 下載過程中的錯誤
func DownloadFileFromURL(ctx context.Context, url, messageType string) (localPath, mimeType string, err error) {
	if g.IsEmpty(url) {
		err = gerror.New("download URL cannot be empty")
		g.Log().Error(ctx, "DownloadFileFromURL URL validation failed:", err)
		return "", "", err
	}

	if g.IsEmpty(messageType) {
		err = gerror.New("message type cannot be empty")
		g.Log().Error(ctx, "DownloadFileFromURL message type validation failed:", err)
		return "", "", err
	}

	g.Log().Infof(ctx, "Starting file download from URL: %s, type: %s", url, messageType)

	// 創建 HTTP 客戶端（使用 GoFrame 的 gclient）
	client := gclient.New()
	client.SetTimeout(30 * time.Second) // 設置 30 秒超時

	// 下載文件
	response, err := client.Get(ctx, url)
	if err != nil {
		err = gerror.Wrapf(err, "failed to download file from URL: %s", url)
		g.Log().Error(ctx, "DownloadFileFromURL HTTP request failed:", err)
		return "", "", err
	}
	defer response.Close()

	// 檢查 HTTP 狀態碼
	if response.StatusCode != http.StatusOK {
		err = gerror.Newf("HTTP request failed with status: %d %s", response.StatusCode, response.Status)
		g.Log().Error(ctx, "DownloadFileFromURL HTTP status error:", err)
		return "", "", err
	}

	// 讀取文件內容
	fileContent, err := io.ReadAll(response.Body)
	if err != nil {
		err = gerror.Wrapf(err, "failed to read response body from URL: %s", url)
		g.Log().Error(ctx, "DownloadFileFromURL read body failed:", err)
		return "", "", err
	}

	// 檢測 MIME 類型
	detectedMime := mimetype.Detect(fileContent)
	mimeType = detectedMime.String()
	g.Log().Debugf(ctx, "Detected MIME type: %s for URL: %s", mimeType, url)

	// 驗證文件類型是否符合預期
	if !isValidFileType(mimeType, messageType) {
		err = gerror.Newf("invalid file type: expected %s, got %s", messageType, mimeType)
		g.Log().Error(ctx, "DownloadFileFromURL file type validation failed:", err)
		return "", "", err
	}

	// 生成臨時文件名
	tempDir := gfile.Temp()
	fileName := fmt.Sprintf("%s_%d%s", messageType, grand.N(100000, 999999), detectedMime.Extension())
	localPath = filepath.Join(tempDir, fileName)

	// 保存文件到臨時目錄
	err = gfile.PutBytes(localPath, fileContent)
	if err != nil {
		err = gerror.Wrapf(err, "failed to save file to: %s", localPath)
		g.Log().Error(ctx, "DownloadFileFromURL save file failed:", err)
		return "", "", err
	}

	g.Log().Infof(ctx, "Successfully downloaded file: %s -> %s (MIME: %s, Size: %d bytes)",
		url, localPath, mimeType, len(fileContent))

	return localPath, mimeType, nil
}

// isValidFileType 驗證文件類型是否符合消息類型
func isValidFileType(mimeType, messageType string) bool {
	switch messageType {
	case consts.MessageTypeImage:
		// 檢查是否為支援的圖片格式
		for _, format := range consts.PhotoFormat {
			if mimeType == format {
				return true
			}
		}
		return false
	case consts.MessageTypeVoice:
		// 檢查是否為支援的音頻格式
		for _, format := range consts.AudioFormat {
			if mimeType == format {
				return true
			}
		}
		return false
	default:
		return false
	}
}

// ConvertAudioToText 使用 default llm (gemini) 將音頻文件轉換為文本
// 這個函數專門用於處理 voice 類型的消息，當租戶配置的模型不是 gemini 時使用
//
// 參數:
//   - ctx: 上下文
//   - audioFilePath: 音頻文件的本地路徑
//   - mimeType: 音頻文件的 MIME 類型
//
// 返回:
//   - text: 轉換後的文本內容
//   - err: 轉換過程中的錯誤
func ConvertAudioToText(ctx context.Context, audioFilePath, mimeType string) (text string, err error) {
	if g.IsEmpty(audioFilePath) {
		err = gerror.New("audio file path cannot be empty")
		g.Log().Error(ctx, "ConvertAudioToText path validation failed:", err)
		return "", err
	}

	if !gfile.Exists(audioFilePath) {
		err = gerror.Newf("audio file does not exist: %s", audioFilePath)
		g.Log().Error(ctx, "ConvertAudioToText file existence check failed:", err)
		return "", err
	}

	g.Log().Infof(ctx, "Starting audio to text conversion: %s (MIME: %s)", audioFilePath, mimeType)

	// 獲取 default llm 配置
	var vDefaultLLMs *g.Var
	vDefaultLLMs, err = g.Cfg().Get(ctx, "default_llms")
	if err != nil {
		err = gerror.Wrapf(err, "failed to get default_llms config")
		g.Log().Error(ctx, "ConvertAudioToText config retrieval failed:", err)
		return "", err
	}

	var defaultLLMsConfig *llm.LLMsConfig
	err = vDefaultLLMs.Struct(&defaultLLMsConfig)
	if err != nil {
		err = gerror.Wrapf(err, "failed to parse default_llms config")
		g.Log().Error(ctx, "ConvertAudioToText config parsing failed:", err)
		return "", err
	}

	// 獲取音頻轉文本的 prompt 配置
	var vAudioPrompt *g.Var
	vAudioPrompt, err = g.Cfg().Get(ctx, "prompts.audio_to_text_prompt")
	if err != nil {
		g.Log().Warning(ctx, "Failed to get audio_to_text_prompt config, using default")
		// 使用預設 prompt
		vAudioPrompt = g.NewVar("請將這個音頻文件轉換為文本，只返回轉換後的文本內容，不要添加任何額外的說明或格式。")
	}

	audioPrompt := vAudioPrompt.String()
	if g.IsEmpty(audioPrompt) {
		audioPrompt = "請將這個音頻文件轉換為文本，只返回轉換後的文本內容，不要添加任何額外的說明或格式。"
	}

	g.Log().Debugf(ctx, "Using audio conversion prompt: %s", audioPrompt)

	// 創建 GenerateContent 請求
	request := &llm.GenerateContentRequest{
		Prompt:           audioPrompt,
		MaxContinuations: 1, // 音頻轉文本通常不需要續寫
		TotalTokenBudget: 4096,
		IncludeThinking:  false, // 音頻轉文本不需要思考過程
	}

	// 這裡需要創建一個 Gemini 實例來處理音頻轉文本
	// 由於我們需要使用 default llm，我們需要創建一個臨時的 Gemini 實例
	geminiLLM, err := createDefaultGeminiInstance(ctx, defaultLLMsConfig, audioFilePath, mimeType)
	if err != nil {
		err = gerror.Wrapf(err, "failed to create default Gemini instance")
		g.Log().Error(ctx, "ConvertAudioToText Gemini creation failed:", err)
		return "", err
	}

	// 使用 GenerateContent 接口進行音頻轉文本
	response, err := geminiLLM.GenerateContent(ctx, request)
	if err != nil {
		err = gerror.Wrapf(err, "failed to convert audio to text using Gemini")
		g.Log().Error(ctx, "ConvertAudioToText GenerateContent failed:", err)
		return "", err
	}

	if response == nil || g.IsEmpty(response.OutputContent) {
		err = gerror.New("audio to text conversion returned empty result")
		g.Log().Error(ctx, "ConvertAudioToText empty result:", err)
		return "", err
	}

	text = response.OutputContent
	g.Log().Infof(ctx, "Successfully converted audio to text: %s -> %d characters",
		audioFilePath, len(text))

	// 記錄轉換統計信息
	g.Log().Debugf(ctx, "Audio conversion stats - input_tokens: %d, output_tokens: %d, generation_time: %dms",
		response.InputTokens, response.OutputTokens, response.GenerationTime)

	return text, nil
}

// createDefaultGeminiInstance 創建一個用於音頻轉文本的 default Gemini 實例
// 這個函數專門用於音頻轉文本場景，創建一個臨時的 Gemini 實例
func createDefaultGeminiInstance(ctx context.Context, config *llm.LLMsConfig, audioFilePath, mimeType string) (geminiLLM interface{}, err error) {
	// 由於我們需要導入 gemini 包，這裡需要使用反射或者接口的方式
	// 為了簡化實現，我們返回一個錯誤，提示需要在調用方處理
	err = gerror.New("createDefaultGeminiInstance needs to be implemented in the calling context with proper gemini import")
	g.Log().Error(ctx, "createDefaultGeminiInstance not implemented:", err)
	return nil, err
}
